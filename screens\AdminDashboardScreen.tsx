import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Alert,
  Platform
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Ionicons } from '@expo/vector-icons';
import { toast } from 'sonner-native';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import ErrorAnalytics from '../components/ErrorAnalytics';
import UserManagement from '../components/UserManagement';
import { adminAPI, User } from '../lib/supabase-api';

// Sample data for charts
const ATTENDANCE_DATA = {
  labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'],
  datasets: [
    {
      data: [30, 45, 28, 50, 42],
      color: '#4A80F0',
      label: 'In Office'
    },
    {
      data: [15, 10, 25, 8, 12],
      color: '#34C759',
      label: 'WFH'
    },
    {
      data: [5, 3, 7, 2, 6],
      color: '#FF9500',
      label: 'On Leave'
    }
  ]
};

const PARKING_USAGE = {
  labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'],
  datasets: [
    {
      data: [38, 45, 30, 48, 42],
      color: '#FF9500',
      label: 'Car'
    },
    {
      data: [25, 30, 18, 32, 28],
      color: '#34C759',
      label: 'Bike'
    }
  ]
};

const ROOM_OCCUPANCY = {
  labels: ['Falcon', 'Eagle', 'Hawk', 'Sparrow', 'Condor'],
  datasets: [
    {
      data: [85, 65, 50, 30, 90],
      color: '#4A80F0',
      label: 'Utilization %'
    }
  ]
};

// Helper to create a simple bar chart
interface BarChartDataset {
  data: number[];
  color: string;
  label: string;
}
interface BarChartData {
  labels: string[];
  datasets: BarChartDataset[];
}
const SimpleBarChart = ({ data, height = 200, barWidth = 16, spacing = 30 }: { data: BarChartData; height?: number; barWidth?: number; spacing?: number }) => {
  // Find the max value to scale properly
  const maxValue = Math.max(...data.datasets.reduce((acc: number[], dataset: BarChartDataset) => {
    return [...acc, ...dataset.data];
  }, []));
  
  const screenWidth = Dimensions.get('window').width - 32; // Full width minus padding
  const chartWidth = data.labels.length * (data.datasets.length * barWidth + spacing);
  const containerWidth = Math.max(screenWidth, chartWidth);
  
  return (
    <View style={{ height, width: containerWidth }}>
      {/* Y-Axis labels */}
      <View style={styles.yAxisLabels}>
        <Text style={styles.axisLabel}>{maxValue}</Text>
        <Text style={styles.axisLabel}>{Math.round(maxValue * 0.75)}</Text>
        <Text style={styles.axisLabel}>{Math.round(maxValue * 0.5)}</Text>
        <Text style={styles.axisLabel}>{Math.round(maxValue * 0.25)}</Text>
        <Text style={styles.axisLabel}>0</Text>
      </View>
      
      {/* Chart grid */}
      <View style={styles.chartGrid}>
        <View style={[styles.gridLine, { top: 0 }]} />
        <View style={[styles.gridLine, { top: '25%' }]} />
        <View style={[styles.gridLine, { top: '50%' }]} />
        <View style={[styles.gridLine, { top: '75%' }]} />
        <View style={[styles.gridLine, { top: '100%' }]} />
      </View>
      
      {/* Bars */}
      <View style={styles.barsContainer}>
        {data.labels.map((label: string, labelIndex: number) => (
          <View key={label} style={styles.barGroup}>
            {data.datasets.map((dataset: BarChartDataset, datasetIndex: number) => (
              <View 
                key={`${label}-${datasetIndex}`}
                style={[
                  styles.bar, 
                  { 
                    height: `${(dataset.data[labelIndex] / maxValue) * 100}%`,
                    backgroundColor: dataset.color,
                    width: barWidth
                  }
                ]}
              />
            ))}
            <Text style={styles.barLabel}>{label}</Text>
          </View>
        ))}
      </View>
    </View>
  );
};

// Legend component
const ChartLegend = ({ datasets }: { datasets: BarChartDataset[] }) => (
  <View style={styles.legendContainer}>
    {datasets.map((dataset: BarChartDataset, index: number) => (
      <View key={index} style={styles.legendItem}>
        <View style={[styles.legendColor, { backgroundColor: dataset.color }]} />
        <Text style={styles.legendText}>{dataset.label}</Text>
      </View>
    ))}
  </View>
);

// Simple card stats
const StatCard = ({ title, value, icon, color, subtitle }: { title: string; value: string; icon: string; color: string; subtitle?: string }) => (
  <View style={styles.statCard}>
    <View style={styles.statHeader}>
      <Text style={styles.statTitle}>{title}</Text>
      <View style={[styles.statIconContainer, { backgroundColor: color }]}>
        <Ionicons name={icon} size={20} color="white" />
      </View>
    </View>
    <Text style={styles.statValue}>{value}</Text>
    {subtitle && <Text style={styles.statSubtitle}>{subtitle}</Text>}
  </View>
);

export default function AdminDashboardScreen() {
  const navigation = useNavigation();
  const [activeTimeRange, setActiveTimeRange] = useState<string>('week');
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [users, setUsers] = useState<User[]>([]);
  const [loadingUsers, setLoadingUsers] = useState<boolean>(false);

  const handleDeleteUser = async (id: string) => {
    if (!window.confirm('Are you sure you want to delete this user?')) return;
    await adminAPI.softDeleteUser(id);
    setUsers((users: User[]) => users.filter((u: User) => u.id !== id));
  };

  // Data export functionality
  const exportData = async (dataType: string, format: 'csv' | 'json') => {
    try {
      toast.info(`Preparing ${dataType} export...`);

      let data: any[] = [];
      let filename = '';

      switch (dataType) {
        case 'attendance':
          // Mock attendance data - replace with actual API call
          data = [
            { date: '2024-01-15', user_id: 'user1', status: 'office', check_in: '09:00', check_out: '17:30' },
            { date: '2024-01-15', user_id: 'user2', status: 'wfh', check_in: '09:15', check_out: '18:00' },
            { date: '2024-01-16', user_id: 'user1', status: 'office', check_in: '08:45', check_out: '17:15' },
          ];
          filename = `attendance_export_${new Date().toISOString().split('T')[0]}`;
          break;

        case 'bookings':
          // Mock booking data - replace with actual API call
          data = [
            { id: 'book1', room_name: 'Falcon', user_id: 'user1', date: '2024-01-15', start_time: '10:00', end_time: '11:00', purpose: 'Team Meeting' },
            { id: 'book2', room_name: 'Eagle', user_id: 'user2', date: '2024-01-15', start_time: '14:00', end_time: '15:30', purpose: 'Client Call' },
          ];
          filename = `bookings_export_${new Date().toISOString().split('T')[0]}`;
          break;

        case 'parking':
          // Mock parking data - replace with actual API call
          data = [
            { id: 'park1', user_id: 'user1', date: '2024-01-15', start_time: '09:00', end_time: '17:30', vehicle_type: 'car' },
            { id: 'park2', user_id: 'user3', date: '2024-01-15', start_time: '08:30', end_time: '17:00', vehicle_type: 'bike' },
          ];
          filename = `parking_export_${new Date().toISOString().split('T')[0]}`;
          break;

        case 'users':
          // Mock user data - replace with actual API call
          data = [
            { id: 'user1', email: '<EMAIL>', full_name: 'John Doe', employee_id: 'EMP001', work_mode: 'hybrid' },
            { id: 'user2', email: '<EMAIL>', full_name: 'Jane Smith', employee_id: 'EMP002', work_mode: 'office' },
          ];
          filename = `users_export_${new Date().toISOString().split('T')[0]}`;
          break;

        default:
          throw new Error('Invalid data type');
      }

      let fileContent = '';
      let fileExtension = '';

      if (format === 'csv') {
        // Convert to CSV
        if (data.length > 0) {
          const headers = Object.keys(data[0]).join(',');
          const rows = data.map(row => Object.values(row).join(',')).join('\n');
          fileContent = `${headers}\n${rows}`;
        }
        fileExtension = 'csv';
      } else {
        // Convert to JSON
        fileContent = JSON.stringify(data, null, 2);
        fileExtension = 'json';
      }

      // Save file
      const fileUri = `${FileSystem.documentDirectory}${filename}.${fileExtension}`;
      await FileSystem.writeAsStringAsync(fileUri, fileContent);

      // Share file
      if (await Sharing.isAvailableAsync()) {
        await Sharing.shareAsync(fileUri, {
          mimeType: format === 'csv' ? 'text/csv' : 'application/json',
          dialogTitle: `Export ${dataType} data`
        });
        toast.success(`${dataType} data exported successfully!`);
      } else {
        toast.success(`${dataType} data saved to: ${fileUri}`);
      }

    } catch (error) {
      console.error('Export error:', error);
      toast.error(`Failed to export ${dataType} data`);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#222B45" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Admin Dashboard</Text>
        <View style={{ width: 40 }} /> {/* Empty view for alignment */}
      </View>

      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'overview' && styles.activeTab]}
          onPress={() => setActiveTab('overview')}
        >
          <Ionicons
            name="analytics-outline"
            size={20}
            color={activeTab === 'overview' ? '#4A80F0' : '#999'}
          />
          <Text style={[styles.tabText, activeTab === 'overview' && styles.activeTabText]}>
            Overview
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'users' && styles.activeTab]}
          onPress={() => setActiveTab('users')}
        >
          <Ionicons
            name="people-outline"
            size={20}
            color={activeTab === 'users' ? '#4A80F0' : '#999'}
          />
          <Text style={[styles.tabText, activeTab === 'users' && styles.activeTabText]}>
            Users
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'errors' && styles.activeTab]}
          onPress={() => setActiveTab('errors')}
        >
          <Ionicons
            name="bug-outline"
            size={20}
            color={activeTab === 'errors' ? '#4A80F0' : '#999'}
          />
          <Text style={[styles.tabText, activeTab === 'errors' && styles.activeTabText]}>
            Error Logs
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'export' && styles.activeTab]}
          onPress={() => setActiveTab('export')}
        >
          <Ionicons
            name="download-outline"
            size={20}
            color={activeTab === 'export' ? '#4A80F0' : '#999'}
          />
          <Text style={[styles.tabText, activeTab === 'export' && styles.activeTabText]}>
            Export
          </Text>
        </TouchableOpacity>
      </View>

      {/* Tab Content */}
      {activeTab === 'overview' ? (
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Quick Stats */}
          <View style={styles.section}>
          <Text style={styles.sectionTitle}>Today's Overview</Text>
          <View style={styles.statsRow}>
            <StatCard 
              title="Attendance"
              value="50/60"
              subtitle="83% of employees"
              icon="people"
              color="#4A80F0"
            />
            <StatCard 
              title="Parking"
              value="38/50"
              subtitle="76% occupied"
              icon="car"
              color="#FF9500"
            />
          </View>
          <View style={styles.statsRow}>
            <StatCard 
              title="Rooms Booked"
              value="12/15"
              subtitle="80% utilized"
              icon="calendar"
              color="#34C759"
            />
            <StatCard 
              title="WFH"
              value="10"
              subtitle="17% of employees"
              icon="home"
              color="#AF52DE"
            />
          </View>
        </View>
        
        {/* Time Range Selector */}
        <View style={styles.timeRangeContainer}>
          <TouchableOpacity 
            style={[
              styles.timeRangeButton,
              activeTimeRange === 'day' && styles.activeTimeRangeButton
            ]}
            onPress={() => setActiveTimeRange('day')}
          >
            <Text style={[
              styles.timeRangeText,
              activeTimeRange === 'day' && styles.activeTimeRangeText
            ]}>
              Day
            </Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[
              styles.timeRangeButton,
              activeTimeRange === 'week' && styles.activeTimeRangeButton
            ]}
            onPress={() => setActiveTimeRange('week')}
          >
            <Text style={[
              styles.timeRangeText,
              activeTimeRange === 'week' && styles.activeTimeRangeText
            ]}>
              Week
            </Text>
          </TouchableOpacity>
          <TouchableOpacity 
            style={[
              styles.timeRangeButton,
              activeTimeRange === 'month' && styles.activeTimeRangeButton
            ]}
            onPress={() => setActiveTimeRange('month')}
          >
            <Text style={[
              styles.timeRangeText,
              activeTimeRange === 'month' && styles.activeTimeRangeText
            ]}>
              Month
            </Text>
          </TouchableOpacity>
        </View>
        
        {/* Attendance Chart */}
        <View style={styles.chartSection}>
          <Text style={styles.sectionTitle}>Attendance Trends</Text>
          <View style={styles.chartCard}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <SimpleBarChart data={ATTENDANCE_DATA} height={200} />
            </ScrollView>
            <ChartLegend datasets={ATTENDANCE_DATA.datasets} />
          </View>
        </View>
        
        {/* Parking Usage Chart */}
        <View style={styles.chartSection}>
          <Text style={styles.sectionTitle}>Parking Usage</Text>
          <View style={styles.chartCard}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <SimpleBarChart data={PARKING_USAGE} height={200} />
            </ScrollView>
            <ChartLegend datasets={PARKING_USAGE.datasets} />
          </View>
        </View>
        
        {/* Room Occupancy Chart */}
        <View style={styles.chartSection}>
          <Text style={styles.sectionTitle}>Room Occupancy</Text>
          <View style={styles.chartCard}>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <SimpleBarChart data={ROOM_OCCUPANCY} height={200} />
            </ScrollView>
            <ChartLegend datasets={ROOM_OCCUPANCY.datasets} />
          </View>
        </View>
        
        {/* Key Insights */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Key Insights</Text>
          
          <View style={styles.insightCard}>
            <View style={styles.insightIconContainer}>
              <Ionicons name="analytics" size={24} color="#4A80F0" />
            </View>
            <View style={styles.insightContent}>
              <Text style={styles.insightTitle}>Peak Office Day</Text>
              <Text style={styles.insightValue}>Tuesday</Text>
              <Text style={styles.insightDescription}>
                Tuesdays have the highest office attendance with 45 employees on average.
              </Text>
            </View>
          </View>
          
          <View style={styles.insightCard}>
            <View style={styles.insightIconContainer}>
              <Ionicons name="car" size={24} color="#FF9500" />
            </View>
            <View style={styles.insightContent}>
              <Text style={styles.insightTitle}>Parking Availability</Text>
              <Text style={styles.insightValue}>Most Critical on Thursdays</Text>
              <Text style={styles.insightDescription}>
                Consider implementing reserve-ahead parking on Thursdays to avoid shortages.
              </Text>
            </View>
          </View>
          
          <View style={styles.insightCard}>
            <View style={styles.insightIconContainer}>
              <Ionicons name="business" size={24} color="#34C759" />
            </View>
            <View style={styles.insightContent}>
              <Text style={styles.insightTitle}>Room Utilization</Text>
              <Text style={styles.insightValue}>Falcon & Condor at 85%+</Text>
              <Text style={styles.insightDescription}>
                These rooms are almost at capacity. Consider expanding similar facilities.
              </Text>
            </View>
          </View>
          
          <View style={styles.insightCard}>
            <View style={styles.insightIconContainer}>
              <Ionicons name="time" size={24} color="#AF52DE" />
            </View>
            <View style={styles.insightContent}>
              <Text style={styles.insightTitle}>Meeting No-Shows</Text>
              <Text style={styles.insightValue}>12% of bookings</Text>
              <Text style={styles.insightDescription}>
                Implementing 10-min auto-release could increase room availability by 15%.
              </Text>
            </View>
          </View>
        </View>
        </ScrollView>
      ) : activeTab === 'users' ? (
        <UserManagement />
      ) : activeTab === 'errors' ? (
        <ErrorAnalytics />
      ) : (
        // Export Tab Content
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Data Export</Text>
            <Text style={styles.exportDescription}>
              Export your office data in CSV or JSON format for analysis and reporting.
            </Text>
          </View>

          {/* Export Cards */}
          <View style={styles.section}>
            <View style={styles.exportCard}>
              <View style={styles.exportCardHeader}>
                <View style={styles.exportIconContainer}>
                  <Ionicons name="people" size={24} color="#4A80F0" />
                </View>
                <View style={styles.exportCardContent}>
                  <Text style={styles.exportCardTitle}>Attendance Data</Text>
                  <Text style={styles.exportCardDescription}>
                    Export daily attendance records, check-in/out times, and work modes
                  </Text>
                </View>
              </View>
              <View style={styles.exportButtons}>
                <TouchableOpacity
                  style={[styles.exportButton, styles.csvButton]}
                  onPress={() => exportData('attendance', 'csv')}
                >
                  <Ionicons name="document-text" size={16} color="#34C759" />
                  <Text style={[styles.exportButtonText, { color: '#34C759' }]}>CSV</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.exportButton, styles.jsonButton]}
                  onPress={() => exportData('attendance', 'json')}
                >
                  <Ionicons name="code" size={16} color="#FF9500" />
                  <Text style={[styles.exportButtonText, { color: '#FF9500' }]}>JSON</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.exportCard}>
              <View style={styles.exportCardHeader}>
                <View style={styles.exportIconContainer}>
                  <Ionicons name="calendar" size={24} color="#34C759" />
                </View>
                <View style={styles.exportCardContent}>
                  <Text style={styles.exportCardTitle}>Room Bookings</Text>
                  <Text style={styles.exportCardDescription}>
                    Export meeting room reservations, schedules, and utilization data
                  </Text>
                </View>
              </View>
              <View style={styles.exportButtons}>
                <TouchableOpacity
                  style={[styles.exportButton, styles.csvButton]}
                  onPress={() => exportData('bookings', 'csv')}
                >
                  <Ionicons name="document-text" size={16} color="#34C759" />
                  <Text style={[styles.exportButtonText, { color: '#34C759' }]}>CSV</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.exportButton, styles.jsonButton]}
                  onPress={() => exportData('bookings', 'json')}
                >
                  <Ionicons name="code" size={16} color="#FF9500" />
                  <Text style={[styles.exportButtonText, { color: '#FF9500' }]}>JSON</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.exportCard}>
              <View style={styles.exportCardHeader}>
                <View style={styles.exportIconContainer}>
                  <Ionicons name="car" size={24} color="#FF9500" />
                </View>
                <View style={styles.exportCardContent}>
                  <Text style={styles.exportCardTitle}>Parking Reservations</Text>
                  <Text style={styles.exportCardDescription}>
                    Export parking spot reservations and vehicle information
                  </Text>
                </View>
              </View>
              <View style={styles.exportButtons}>
                <TouchableOpacity
                  style={[styles.exportButton, styles.csvButton]}
                  onPress={() => exportData('parking', 'csv')}
                >
                  <Ionicons name="document-text" size={16} color="#34C759" />
                  <Text style={[styles.exportButtonText, { color: '#34C759' }]}>CSV</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.exportButton, styles.jsonButton]}
                  onPress={() => exportData('parking', 'json')}
                >
                  <Ionicons name="code" size={16} color="#FF9500" />
                  <Text style={[styles.exportButtonText, { color: '#FF9500' }]}>JSON</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.exportCard}>
              <View style={styles.exportCardHeader}>
                <View style={styles.exportIconContainer}>
                  <Ionicons name="person" size={24} color="#AF52DE" />
                </View>
                <View style={styles.exportCardContent}>
                  <Text style={styles.exportCardTitle}>User Data</Text>
                  <Text style={styles.exportCardDescription}>
                    Export employee information, preferences, and profile data
                  </Text>
                </View>
              </View>
              <View style={styles.exportButtons}>
                <TouchableOpacity
                  style={[styles.exportButton, styles.csvButton]}
                  onPress={() => exportData('users', 'csv')}
                >
                  <Ionicons name="document-text" size={16} color="#34C759" />
                  <Text style={[styles.exportButtonText, { color: '#34C759' }]}>CSV</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.exportButton, styles.jsonButton]}
                  onPress={() => exportData('users', 'json')}
                >
                  <Ionicons name="code" size={16} color="#FF9500" />
                  <Text style={[styles.exportButtonText, { color: '#FF9500' }]}>JSON</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* Export Info */}
          <View style={styles.section}>
            <View style={styles.infoCard}>
              <Ionicons name="information-circle" size={24} color="#4A80F0" />
              <View style={styles.infoContent}>
                <Text style={styles.infoTitle}>Export Information</Text>
                <Text style={styles.infoText}>
                  • CSV files can be opened in Excel or Google Sheets{'\n'}
                  • JSON files are suitable for technical analysis{'\n'}
                  • Data includes records from the last 30 days{'\n'}
                  • Files are automatically shared via your device's sharing options
                </Text>
              </View>
            </View>
          </View>
        </ScrollView>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FD',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#EDF1F7',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#222B45',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#F8F9FA',
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
    padding: 4,
  },
  tab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 6,
    gap: 8,
  },
  activeTab: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tabText: {
    fontSize: 16,
    color: '#999',
  },
  activeTabText: {
    color: '#4A80F0',
    fontWeight: '600',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#222B45',
    marginBottom: 16,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  statCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    width: '48%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  statTitle: {
    fontSize: 14,
    color: '#8F9BB3',
  },
  statIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statValue: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#222B45',
    marginBottom: 4,
  },
  statSubtitle: {
    fontSize: 12,
    color: '#8F9BB3',
  },
  timeRangeContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 24,
  },
  timeRangeButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    marginHorizontal: 8,
  },
  activeTimeRangeButton: {
    backgroundColor: '#4A80F0',
  },
  timeRangeText: {
    fontSize: 14,
    color: '#8F9BB3',
    fontWeight: '500',
  },
  activeTimeRangeText: {
    color: 'white',
    fontWeight: '600',
  },
  chartSection: {
    marginBottom: 24,
  },
  chartCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  yAxisLabels: {
    position: 'absolute',
    left: 0,
    top: 0,
    bottom: 30, // Space for x-axis labels
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    paddingRight: 8,
    width: 35,
  },
  axisLabel: {
    fontSize: 10,
    color: '#8F9BB3',
  },
  chartGrid: {
    position: 'absolute',
    left: 40, // Space for y-axis labels
    right: 0,
    top: 0,
    bottom: 30, // Space for x-axis labels
  },
  gridLine: {
    position: 'absolute',
    left: 0,
    right: 0,
    height: 1,
    backgroundColor: '#EDF1F7',
  },
  barsContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    position: 'absolute',
    left: 40, // Space for y-axis labels
    right: 0,
    bottom: 30, // Space for x-axis labels
    top: 0,
    justifyContent: 'space-around',
  },
  barGroup: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  bar: {
    marginHorizontal: 2,
    borderTopLeftRadius: 4,
    borderTopRightRadius: 4,
  },
  barLabel: {
    position: 'absolute',
    bottom: -25, // Below the bars
    textAlign: 'center',
    fontSize: 12,
    color: '#8F9BB3',
  },
  legendContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 24,
    paddingLeft: 40, // Align with chart
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
    marginBottom: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 6,
  },
  legendText: {
    fontSize: 12,
    color: '#8F9BB3',
  },
  insightCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    flexDirection: 'row',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  insightIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#F0F7FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  insightContent: {
    flex: 1,
  },
  insightTitle: {
    fontSize: 14,
    color: '#8F9BB3',
    marginBottom: 4,
  },
  insightValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#222B45',
    marginBottom: 4,
  },
  insightDescription: {
    fontSize: 14,
    color: '#8F9BB3',
    lineHeight: 20,
  },
  exportDescription: {
    fontSize: 16,
    color: '#8F9BB3',
    lineHeight: 22,
    marginBottom: 8,
  },
  exportCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  exportCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  exportIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#F0F7FF',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  exportCardContent: {
    flex: 1,
  },
  exportCardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#222B45',
    marginBottom: 4,
  },
  exportCardDescription: {
    fontSize: 14,
    color: '#8F9BB3',
    lineHeight: 20,
  },
  exportButtons: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    borderWidth: 1,
    flex: 0.45,
  },
  csvButton: {
    borderColor: '#34C759',
    backgroundColor: '#F0FFF4',
  },
  jsonButton: {
    borderColor: '#FF9500',
    backgroundColor: '#FFF8F0',
  },
  exportButtonText: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  infoCard: {
    backgroundColor: '#F0F7FF',
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  infoContent: {
    flex: 1,
    marginLeft: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#222B45',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#8F9BB3',
    lineHeight: 20,
  },
});