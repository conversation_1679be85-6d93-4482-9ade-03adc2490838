
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for smart-office-assistant-new/lib</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> smart-office-assistant-new/lib</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">42.01% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>50/119</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">27.39% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>20/73</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">48.14% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>13/27</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">52.74% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>48/91</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="supabase-api.ts"><a href="supabase-api.ts.html">supabase-api.ts</a></td>
	<td data-value="42.01" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 42%"></div><div class="cover-empty" style="width: 58%"></div></div>
	</td>
	<td data-value="42.01" class="pct low">42.01%</td>
	<td data-value="119" class="abs low">50/119</td>
	<td data-value="27.39" class="pct low">27.39%</td>
	<td data-value="73" class="abs low">20/73</td>
	<td data-value="48.14" class="pct low">48.14%</td>
	<td data-value="27" class="abs low">13/27</td>
	<td data-value="52.74" class="pct medium">52.74%</td>
	<td data-value="91" class="abs medium">48/91</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-31T09:28:01.130Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    