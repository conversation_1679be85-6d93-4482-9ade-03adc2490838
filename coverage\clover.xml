<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1748683681917" clover="3.2.0">
  <project timestamp="1748683681918" name="All files">
    <metrics statements="846" coveredstatements="48" conditionals="631" coveredconditionals="20" methods="237" coveredmethods="13" elements="1714" coveredelements="81" complexity="0" loc="846" ncloc="846" packages="3" files="11" classes="11"/>
    <package name="smart-office-assistant-new">
      <metrics statements="133" coveredstatements="0" conditionals="56" coveredconditionals="0" methods="19" coveredmethods="0"/>
      <file name="AuthContext.tsx" path="C:\Users\<USER>\Desktop\AI_APPS\smart-office-assistant-new\AuthContext.tsx">
        <metrics statements="133" coveredstatements="0" conditionals="56" coveredconditionals="0" methods="19" coveredmethods="0"/>
        <line num="48" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="106" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="149" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="158" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="159" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="183" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="204" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="205" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="215" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="219" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="220" count="0" type="stmt"/>
        <line num="224" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="228" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="239" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="251" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="252" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="256" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="257" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="262" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="265" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="277" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="279" count="0" type="stmt"/>
        <line num="283" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="285" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="288" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="292" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="302" count="0" type="stmt"/>
        <line num="303" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="307" count="0" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="310" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="311" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="315" count="0" type="stmt"/>
        <line num="316" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="319" count="0" type="stmt"/>
        <line num="324" count="0" type="stmt"/>
        <line num="326" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="327" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="329" count="0" type="stmt"/>
        <line num="332" count="0" type="stmt"/>
        <line num="333" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="334" count="0" type="stmt"/>
        <line num="335" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="338" count="0" type="stmt"/>
        <line num="340" count="0" type="stmt"/>
        <line num="348" count="0" type="stmt"/>
        <line num="350" count="0" type="stmt"/>
        <line num="354" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="357" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="358" count="0" type="stmt"/>
        <line num="359" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="362" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="372" count="0" type="stmt"/>
        <line num="373" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="374" count="0" type="stmt"/>
        <line num="379" count="0" type="stmt"/>
        <line num="380" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
      </file>
    </package>
    <package name="smart-office-assistant-new.lib">
      <metrics statements="91" coveredstatements="48" conditionals="73" coveredconditionals="20" methods="27" coveredmethods="13"/>
      <file name="supabase-api.ts" path="C:\Users\<USER>\Desktop\AI_APPS\smart-office-assistant-new\lib\supabase-api.ts">
        <metrics statements="91" coveredstatements="48" conditionals="73" coveredconditionals="20" methods="27" coveredmethods="13"/>
        <line num="124" count="1" type="stmt"/>
        <line num="126" count="3" type="stmt"/>
        <line num="127" count="3" type="cond" truecount="2" falsecount="0"/>
        <line num="129" count="1" type="stmt"/>
        <line num="135" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="136" count="1" type="stmt"/>
        <line num="140" count="2" type="stmt"/>
        <line num="147" count="2" type="cond" truecount="2" falsecount="0"/>
        <line num="148" count="1" type="stmt"/>
        <line num="152" count="1" type="stmt"/>
        <line num="158" count="1" type="cond" truecount="2" falsecount="2"/>
        <line num="159" count="1" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="170" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="180" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="181" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="191" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="192" count="0" type="stmt"/>
        <line num="197" count="1" type="stmt"/>
        <line num="199" count="2" type="stmt"/>
        <line num="205" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="206" count="1" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="216" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="217" count="0" type="stmt"/>
        <line num="221" count="1" type="stmt"/>
        <line num="227" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="228" count="1" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="242" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="243" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="254" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="255" count="0" type="stmt"/>
        <line num="259" count="1" type="stmt"/>
        <line num="267" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="268" count="1" type="stmt"/>
        <line num="273" count="1" type="stmt"/>
        <line num="275" count="1" type="stmt"/>
        <line num="282" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="283" count="1" type="stmt"/>
        <line num="287" count="1" type="stmt"/>
        <line num="293" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="294" count="1" type="stmt"/>
        <line num="298" count="1" type="stmt"/>
        <line num="309" count="1" type="cond" truecount="2" falsecount="2"/>
        <line num="310" count="1" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="321" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="322" count="0" type="stmt"/>
        <line num="326" count="0" type="stmt"/>
        <line num="330" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="331" count="0" type="stmt"/>
        <line num="336" count="1" type="stmt"/>
        <line num="338" count="1" type="stmt"/>
        <line num="344" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="345" count="1" type="stmt"/>
        <line num="349" count="1" type="stmt"/>
        <line num="356" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="357" count="1" type="stmt"/>
        <line num="361" count="1" type="stmt"/>
        <line num="368" count="1" type="cond" truecount="1" falsecount="1"/>
        <line num="369" count="1" type="stmt"/>
        <line num="373" count="1" type="stmt"/>
        <line num="375" count="1" type="stmt"/>
        <line num="382" count="1" type="cond" truecount="2" falsecount="2"/>
        <line num="383" count="1" type="stmt"/>
        <line num="388" count="1" type="stmt"/>
        <line num="390" count="0" type="stmt"/>
        <line num="396" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="397" count="0" type="stmt"/>
        <line num="401" count="0" type="stmt"/>
        <line num="408" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="409" count="0" type="stmt"/>
        <line num="414" count="1" type="stmt"/>
        <line num="416" count="0" type="stmt"/>
        <line num="424" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="425" count="0" type="stmt"/>
        <line num="429" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="431" count="0" type="stmt"/>
        <line num="437" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="438" count="0" type="stmt"/>
        <line num="442" count="0" type="stmt"/>
        <line num="447" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="448" count="0" type="stmt"/>
        <line num="452" count="0" type="stmt"/>
        <line num="459" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="460" count="0" type="stmt"/>
      </file>
    </package>
    <package name="smart-office-assistant-new.screens">
      <metrics statements="622" coveredstatements="0" conditionals="502" coveredconditionals="0" methods="191" coveredmethods="0"/>
      <file name="AdminDashboardScreen.tsx" path="C:\Users\<USER>\Desktop\AI_APPS\smart-office-assistant-new\screens\AdminDashboardScreen.tsx">
        <metrics statements="25" coveredstatements="0" conditionals="17" coveredconditionals="0" methods="12" coveredmethods="0"/>
        <line num="15" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="64" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="232" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
      </file>
      <file name="AttendanceScreen.tsx" path="C:\Users\<USER>\Desktop\AI_APPS\smart-office-assistant-new\screens\AttendanceScreen.tsx">
        <metrics statements="88" coveredstatements="0" conditionals="120" coveredconditionals="0" methods="22" coveredmethods="0"/>
        <line num="18" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="99" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="111" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="112" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="177" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="188" count="0" type="stmt"/>
        <line num="189" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="191" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="205" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="280" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
        <line num="322" count="0" type="stmt"/>
        <line num="342" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="376" count="0" type="stmt"/>
        <line num="421" count="0" type="stmt"/>
        <line num="443" count="0" type="stmt"/>
        <line num="454" count="0" type="stmt"/>
        <line num="455" count="0" type="stmt"/>
        <line num="477" count="0" type="stmt"/>
        <line num="478" count="0" type="stmt"/>
        <line num="500" count="0" type="stmt"/>
        <line num="501" count="0" type="stmt"/>
        <line num="523" count="0" type="stmt"/>
        <line num="524" count="0" type="stmt"/>
        <line num="553" count="0" type="stmt"/>
        <line num="569" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="570" count="0" type="stmt"/>
        <line num="571" count="0" type="stmt"/>
        <line num="573" count="0" type="stmt"/>
        <line num="585" count="0" type="stmt"/>
      </file>
      <file name="BookRoomScreen.tsx" path="C:\Users\<USER>\Desktop\AI_APPS\smart-office-assistant-new\screens\BookRoomScreen.tsx">
        <metrics statements="136" coveredstatements="0" conditionals="114" coveredconditionals="0" methods="36" coveredmethods="0"/>
        <line num="21" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="31" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="32" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="124" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="132" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="158" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="171" count="0" type="stmt"/>
        <line num="174" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="175" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="179" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="186" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="187" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="200" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="201" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="208" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="212" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="217" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="228" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="233" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="246" count="0" type="stmt"/>
        <line num="247" count="0" type="stmt"/>
        <line num="250" count="0" type="stmt"/>
        <line num="251" count="0" type="stmt"/>
        <line num="253" count="0" type="stmt"/>
        <line num="257" count="0" type="stmt"/>
        <line num="260" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="328" count="0" type="stmt"/>
        <line num="331" count="0" type="stmt"/>
        <line num="337" count="0" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="406" count="0" type="stmt"/>
        <line num="427" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="435" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="489" count="0" type="stmt"/>
        <line num="502" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="503" count="0" type="stmt"/>
        <line num="512" count="0" type="stmt"/>
        <line num="518" count="0" type="stmt"/>
        <line num="537" count="0" type="stmt"/>
        <line num="561" count="0" type="stmt"/>
      </file>
      <file name="ChatbotScreen.tsx" path="C:\Users\<USER>\Desktop\AI_APPS\smart-office-assistant-new\screens\ChatbotScreen.tsx">
        <metrics statements="87" coveredstatements="0" conditionals="70" coveredconditionals="0" methods="30" coveredmethods="0"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="55" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="91" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="115" count="0" type="stmt"/>
        <line num="116" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="133" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="137" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="138" count="0" type="stmt"/>
        <line num="139" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="150" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="151" count="0" type="stmt"/>
        <line num="153" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="160" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="161" count="0" type="stmt"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="177" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="178" count="0" type="stmt"/>
        <line num="180" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="192" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="197" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="200" count="0" type="stmt"/>
        <line num="201" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="229" count="0" type="stmt"/>
        <line num="269" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="284" count="0" type="stmt"/>
        <line num="289" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="304" count="0" type="stmt"/>
        <line num="309" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
      </file>
      <file name="HomeScreen.tsx" path="C:\Users\<USER>\Desktop\AI_APPS\smart-office-assistant-new\screens\HomeScreen.tsx">
        <metrics statements="29" coveredstatements="0" conditionals="24" coveredconditionals="0" methods="15" coveredmethods="0"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="30" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="277" count="0" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
      </file>
      <file name="OnboardingScreen.tsx" path="C:\Users\<USER>\Desktop\AI_APPS\smart-office-assistant-new\screens\OnboardingScreen.tsx">
        <metrics statements="128" coveredstatements="0" conditionals="65" coveredconditionals="0" methods="45" coveredmethods="0"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="54" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="79" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="97" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="107" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="117" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="120" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="126" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="127" count="0" type="stmt"/>
        <line num="128" count="0" type="stmt"/>
        <line num="130" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="137" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="141" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="147" count="0" type="stmt"/>
        <line num="148" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="162" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="163" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="172" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="182" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="183" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="193" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="197" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="212" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="222" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="223" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="227" count="0" type="stmt"/>
        <line num="230" count="0" type="stmt"/>
        <line num="236" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="242" count="0" type="stmt"/>
        <line num="249" count="0" type="stmt"/>
        <line num="252" count="0" type="stmt"/>
        <line num="254" count="0" type="stmt"/>
        <line num="257" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="258" count="0" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="261" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="266" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="stmt"/>
        <line num="275" count="0" type="stmt"/>
        <line num="276" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="286" count="0" type="stmt"/>
        <line num="291" count="0" type="stmt"/>
        <line num="292" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="294" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="298" count="0" type="stmt"/>
        <line num="300" count="0" type="stmt"/>
        <line num="305" count="0" type="stmt"/>
        <line num="306" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="308" count="0" type="stmt"/>
        <line num="310" count="0" type="stmt"/>
        <line num="312" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="318" count="0" type="stmt"/>
        <line num="333" count="0" type="stmt"/>
        <line num="412" count="0" type="stmt"/>
        <line num="418" count="0" type="stmt"/>
        <line num="443" count="0" type="stmt"/>
      </file>
      <file name="ParkingScreen.tsx" path="C:\Users\<USER>\Desktop\AI_APPS\smart-office-assistant-new\screens\ParkingScreen.tsx">
        <metrics statements="61" coveredstatements="0" conditionals="50" coveredconditionals="0" methods="19" coveredmethods="0"/>
        <line num="17" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="71" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="72" count="0" type="stmt"/>
        <line num="74" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="87" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="88" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="111" count="0" type="stmt"/>
        <line num="113" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="114" count="0" type="stmt"/>
        <line num="121" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="139" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="148" count="0" type="stmt"/>
        <line num="155" count="0" type="stmt"/>
        <line num="165" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="170" count="0" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
        <line num="241" count="0" type="stmt"/>
        <line num="278" count="0" type="stmt"/>
        <line num="301" count="0" type="stmt"/>
      </file>
      <file name="ProfileScreen.tsx" path="C:\Users\<USER>\Desktop\AI_APPS\smart-office-assistant-new\screens\ProfileScreen.tsx">
        <metrics statements="17" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="36" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="38" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="212" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="224" count="0" type="stmt"/>
      </file>
      <file name="SignInScreen.tsx" path="C:\Users\<USER>\Desktop\AI_APPS\smart-office-assistant-new\screens\SignInScreen.tsx">
        <metrics statements="51" coveredstatements="0" conditionals="30" coveredconditionals="0" methods="7" coveredmethods="0"/>
        <line num="21" count="0" type="stmt"/>
        <line num="22" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="34" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="40" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="41" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="50" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="81" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="89" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="119" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="137" count="0" type="stmt"/>
        <line num="139" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="151" count="0" type="stmt"/>
        <line num="159" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
