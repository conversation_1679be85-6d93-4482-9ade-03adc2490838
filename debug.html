<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Office Assistant - Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #4A80F0;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #3a6bc7;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Smart Office Assistant - Debug Page</h1>
        
        <div class="status info">
            <strong>Debug Information:</strong>
            <ul>
                <li>Environment variables have been configured</li>
                <li>AuthContext has been enhanced with better error handling</li>
                <li>Supabase configuration validation added</li>
                <li>Debug logging enabled</li>
            </ul>
        </div>

        <h2>Environment Check</h2>
        <div id="env-check">
            <button onclick="checkEnvironment()">Check Environment Variables</button>
            <div id="env-results"></div>
        </div>

        <h2>Authentication Test</h2>
        <div id="auth-test">
            <button onclick="testAuth()">Test Authentication Setup</button>
            <div id="auth-results"></div>
        </div>

        <h2>Console Logs</h2>
        <div id="console-logs">
            <button onclick="clearLogs()">Clear Logs</button>
            <button onclick="refreshLogs()">Refresh Logs</button>
            <pre id="log-output">Console logs will appear here...</pre>
        </div>
    </div>

    <script>
        let logs = [];
        
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        console.log = function(...args) {
            logs.push({type: 'log', message: args.join(' '), timestamp: new Date().toISOString()});
            originalLog.apply(console, args);
            updateLogDisplay();
        };
        
        console.error = function(...args) {
            logs.push({type: 'error', message: args.join(' '), timestamp: new Date().toISOString()});
            originalError.apply(console, args);
            updateLogDisplay();
        };
        
        console.warn = function(...args) {
            logs.push({type: 'warn', message: args.join(' '), timestamp: new Date().toISOString()});
            originalWarn.apply(console, args);
            updateLogDisplay();
        };

        function updateLogDisplay() {
            const logOutput = document.getElementById('log-output');
            logOutput.textContent = logs.map(log => 
                `[${log.timestamp}] ${log.type.toUpperCase()}: ${log.message}`
            ).join('\n');
        }

        function clearLogs() {
            logs = [];
            updateLogDisplay();
        }

        function refreshLogs() {
            updateLogDisplay();
        }

        function checkEnvironment() {
            const results = document.getElementById('env-results');
            const envVars = {
                'EXPO_PUBLIC_SUPABASE_URL': process?.env?.EXPO_PUBLIC_SUPABASE_URL || 'Not available in browser',
                'EXPO_PUBLIC_SUPABASE_ANON_KEY': process?.env?.EXPO_PUBLIC_SUPABASE_ANON_KEY ? 'SET' : 'Not available in browser',
                'EXPO_PUBLIC_ENABLE_DEBUG_LOGGING': process?.env?.EXPO_PUBLIC_ENABLE_DEBUG_LOGGING || 'Not available in browser'
            };
            
            results.innerHTML = '<div class="status info"><pre>' + JSON.stringify(envVars, null, 2) + '</pre></div>';
        }

        function testAuth() {
            const results = document.getElementById('auth-results');
            results.innerHTML = '<div class="status info">Testing authentication setup...</div>';
            
            // This would normally test the actual auth setup
            // For now, just show that the debug page is working
            setTimeout(() => {
                results.innerHTML = `
                    <div class="status success">
                        <strong>Debug page is working!</strong><br>
                        - Environment file created<br>
                        - AuthContext enhanced with debugging<br>
                        - Error handling improved<br>
                        - Ready for testing with actual app
                    </div>
                `;
            }, 1000);
        }

        // Initialize
        console.log('Debug page loaded successfully');
        console.log('Environment variables configured');
        console.log('AuthContext debugging enabled');
    </script>
</body>
</html>
